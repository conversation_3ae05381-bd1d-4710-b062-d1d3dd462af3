import { getResellerBizService } from './src/services/resellerbiz';
import { logger } from './src/utils/logger';
import fs from 'fs';
import path from 'path';

/**
 * Test script to fetch all TLDs and their prices from ResellerBiz API
 * This script will:
 * 1. Fetch all product details from ResellerBiz
 * 2. Filter domain-related products (TLDs)
 * 3. Extract pricing information
 * 4. Log the results and save to a file
 */

interface TLDPricing {
  tld: string;
  productKey: string;
  name?: string;
  description?: string;
  registrationPrice?: number;
  renewalPrice?: number;
  transferPrice?: number;
  currency?: string;
  category?: string;
  features?: string[];
  rawData?: any;
}

async function fetchAllTLDs(): Promise<void> {
  try {
    logger.info('Starting TLD pricing fetch from ResellerBiz API...');
    
    const resellerBizService = getResellerBizService();
    
    // Fetch aggregated product data
    logger.info('Fetching aggregated product data...');
    const aggregatedData = await resellerBizService.getAggregatedProductData();
    
    if (!aggregatedData.success) {
      logger.error('Failed to fetch product data:', aggregatedData.error);
      return;
    }
    
    logger.info(`Successfully fetched ${aggregatedData.summary.totalProducts} products across ${aggregatedData.summary.totalCategories} categories`);
    
    // Extract TLD-related products
    const tldPricingData: TLDPricing[] = [];
    const allProducts = aggregatedData.allProducts;
    
    // Look for domain-related categories and products
    const domainCategories = Object.keys(aggregatedData.productsByCategory).filter(category => 
      category.toLowerCase().includes('domain') || 
      category.toLowerCase().includes('tld') ||
      category.toLowerCase().includes('registration') ||
      category.toLowerCase().includes('extension')
    );
    
    logger.info(`Found ${domainCategories.length} domain-related categories:`, domainCategories);
    
    // Process all products to find TLD pricing
    Object.keys(allProducts).forEach(productKey => {
      const product = allProducts[productKey];
      
      // Check if this product is domain/TLD related
      const isDomainProduct = (
        productKey.toLowerCase().includes('domain') ||
        productKey.toLowerCase().includes('tld') ||
        productKey.toLowerCase().includes('.') ||
        (product.name && product.name.toLowerCase().includes('domain')) ||
        (product.description && product.description.toLowerCase().includes('domain')) ||
        (product.type && product.type.toLowerCase().includes('domain'))
      );
      
      if (isDomainProduct) {
        // Extract TLD from product key or name
        let tld = '';
        if (productKey.includes('.')) {
          // Extract TLD from product key like "domreg-com" or "domain.com"
          const parts = productKey.split(/[-._]/);
          tld = parts.find(part => part.length >= 2 && part.length <= 10) || '';
        } else if (product.name && product.name.includes('.')) {
          const match = product.name.match(/\.([a-zA-Z]{2,10})/);
          tld = match ? match[1] : '';
        }
        
        if (!tld && productKey.length <= 10) {
          tld = productKey; // Assume short product keys are TLDs
        }
        
        const tldData: TLDPricing = {
          tld: tld || productKey,
          productKey,
          name: product.name,
          description: product.description,
          currency: product.currency || 'USD',
          features: product.features,
          rawData: product
        };
        
        // Extract pricing information
        if (product.price !== undefined) {
          tldData.registrationPrice = product.price;
        }
        
        // Look for different price types in the raw data
        if (typeof product === 'object') {
          Object.keys(product).forEach(key => {
            const lowerKey = key.toLowerCase();
            if (lowerKey.includes('renew') && typeof product[key] === 'number') {
              tldData.renewalPrice = product[key];
            } else if (lowerKey.includes('transfer') && typeof product[key] === 'number') {
              tldData.transferPrice = product[key];
            } else if (lowerKey.includes('registration') && typeof product[key] === 'number') {
              tldData.registrationPrice = product[key];
            }
          });
        }
        
        // Find category for this product
        Object.keys(aggregatedData.productsByCategory).forEach(category => {
          const categoryData = aggregatedData.productsByCategory[category];
          if (categoryData.products[productKey]) {
            tldData.category = category;
          }
        });
        
        tldPricingData.push(tldData);
      }
    });
    
    // Sort TLDs alphabetically
    tldPricingData.sort((a, b) => a.tld.localeCompare(b.tld));
    
    logger.info(`Found ${tldPricingData.length} TLD-related products`);
    
    // Log summary
    console.log('\n=== TLD PRICING SUMMARY ===');
    console.log(`Total TLDs found: ${tldPricingData.length}`);
    console.log(`Categories: ${domainCategories.join(', ')}`);
    
    // Log detailed TLD information
    console.log('\n=== DETAILED TLD PRICING ===');
    tldPricingData.forEach((tld, index) => {
      console.log(`\n${index + 1}. TLD: .${tld.tld}`);
      console.log(`   Product Key: ${tld.productKey}`);
      console.log(`   Name: ${tld.name || 'N/A'}`);
      console.log(`   Category: ${tld.category || 'N/A'}`);
      console.log(`   Registration Price: ${tld.registrationPrice ? `${tld.registrationPrice} ${tld.currency}` : 'N/A'}`);
      console.log(`   Renewal Price: ${tld.renewalPrice ? `${tld.renewalPrice} ${tld.currency}` : 'N/A'}`);
      console.log(`   Transfer Price: ${tld.transferPrice ? `${tld.transferPrice} ${tld.currency}` : 'N/A'}`);
      if (tld.description) {
        console.log(`   Description: ${tld.description}`);
      }
    });
    
    // Save results to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFile = path.join(__dirname, `tld-pricing-${timestamp}.json`);
    
    const outputData = {
      fetchedAt: new Date().toISOString(),
      summary: {
        totalTLDs: tldPricingData.length,
        totalProducts: aggregatedData.summary.totalProducts,
        totalCategories: aggregatedData.summary.totalCategories,
        domainCategories: domainCategories
      },
      tlds: tldPricingData,
      rawCategories: aggregatedData.categoryMappings,
      rawProductsByCategory: aggregatedData.productsByCategory
    };
    
    fs.writeFileSync(outputFile, JSON.stringify(outputData, null, 2));
    logger.info(`Results saved to: ${outputFile}`);
    
    // Also save a simplified CSV format
    const csvFile = path.join(__dirname, `tld-pricing-${timestamp}.csv`);
    const csvHeader = 'TLD,Product Key,Name,Category,Registration Price,Renewal Price,Transfer Price,Currency\n';
    const csvRows = tldPricingData.map(tld => 
      `"${tld.tld}","${tld.productKey}","${tld.name || ''}","${tld.category || ''}","${tld.registrationPrice || ''}","${tld.renewalPrice || ''}","${tld.transferPrice || ''}","${tld.currency || ''}"`
    ).join('\n');
    
    fs.writeFileSync(csvFile, csvHeader + csvRows);
    logger.info(`CSV results saved to: ${csvFile}`);
    
    console.log(`\n=== FILES SAVED ===`);
    console.log(`JSON: ${outputFile}`);
    console.log(`CSV: ${csvFile}`);
    
  } catch (error) {
    logger.error('Error fetching TLD pricing:', error);
    console.error('Failed to fetch TLD pricing:', error);
  }
}

// Run the script
if (require.main === module) {
  fetchAllTLDs()
    .then(() => {
      logger.info('TLD pricing fetch completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Script failed:', error);
      process.exit(1);
    });
}

export { fetchAllTLDs };
