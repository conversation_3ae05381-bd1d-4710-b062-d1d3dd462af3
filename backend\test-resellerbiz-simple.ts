import { getResellerBizService } from './src/services/resellerbiz';
import { logger } from './src/utils/logger';
import fs from 'fs';
import path from 'path';

/**
 * Simple test script to debug ResellerBiz API calls
 */

async function testResellerBizAPI(): Promise<void> {
  try {
    logger.info('Testing ResellerBiz API connection...');
    
    const resellerBizService = getResellerBizService();
    
    // Test 1: Fetch product details
    console.log('\n=== TEST 1: Fetching Product Details ===');
    const productResult = await resellerBizService.getProductDetails();
    
    if (productResult.success) {
      console.log(`✅ Product details fetch successful!`);
      console.log(`Total products: ${productResult.totalProducts}`);
      
      // Show first few products
      if (productResult.products) {
        const productKeys = Object.keys(productResult.products).slice(0, 5);
        console.log('\nFirst 5 products:');
        productKeys.forEach((key, index) => {
          const product = productResult.products![key];
          console.log(`${index + 1}. ${key}: ${product.name || 'No name'} - ${product.price || 'No price'}`);
        });
      }
      
      // Save product details to file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const productFile = path.join(__dirname, `products-${timestamp}.json`);
      fs.writeFileSync(productFile, JSON.stringify(productResult, null, 2));
      console.log(`\nProduct details saved to: ${productFile}`);
      
    } else {
      console.log(`❌ Product details fetch failed: ${productResult.error}`);
    }
    
    // Test 2: Fetch category mappings
    console.log('\n=== TEST 2: Fetching Category Mappings ===');
    const categoryResult = await resellerBizService.getCategoryKeysMapping();
    
    if (categoryResult.success) {
      console.log(`✅ Category mappings fetch successful!`);
      console.log(`Total categories: ${categoryResult.totalCategories}`);
      
      // Show categories
      if (categoryResult.mappings) {
        const categories = Object.keys(categoryResult.mappings).slice(0, 10);
        console.log('\nFirst 10 categories:');
        categories.forEach((category, index) => {
          const mapping = categoryResult.mappings![category];
          console.log(`${index + 1}. ${category}: ${mapping.productCount} products`);
        });
      }
      
      // Save category mappings to file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const categoryFile = path.join(__dirname, `categories-${timestamp}.json`);
      fs.writeFileSync(categoryFile, JSON.stringify(categoryResult, null, 2));
      console.log(`\nCategory mappings saved to: ${categoryFile}`);
      
    } else {
      console.log(`❌ Category mappings fetch failed: ${categoryResult.error}`);
    }
    
    // Test 3: Test domain availability (simple test)
    console.log('\n=== TEST 3: Testing Domain Availability ===');
    try {
      const domainResult = await resellerBizService.checkDomainAvailability({
        domains: ['testdomain123456'],
        tlds: ['com', 'net']
      });
      
      console.log('✅ Domain availability check successful!');
      console.log('Results:', JSON.stringify(domainResult, null, 2));
      
    } catch (error: any) {
      console.log(`❌ Domain availability check failed: ${error.message}`);
    }
    
  } catch (error) {
    logger.error('Error in ResellerBiz API test:', error);
    console.error('Test failed:', error);
  }
}

// Run the script
if (require.main === module) {
  testResellerBizAPI()
    .then(() => {
      logger.info('ResellerBiz API test completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Test script failed:', error);
      process.exit(1);
    });
}

export { testResellerBizAPI };
