import axios from 'axios';
import { appConfig } from './src/config';
import { logger } from './src/utils/logger';

/**
 * Debug script to test ResellerBiz API endpoints and authentication
 */

async function debugResellerBizAPI(): Promise<void> {
  try {
    console.log('=== ResellerBiz API Debug ===');
    console.log(`Auth User ID: ${appConfig.resellerBiz.authUserId}`);
    console.log(`API Key: ${appConfig.resellerBiz.apiKey ? 'SET' : 'NOT SET'}`);
    console.log(`Base URL: ${appConfig.resellerBiz.baseUrl}`);
    console.log(`Test Mode: ${appConfig.resellerBiz.testMode}`);
    
    // Test 1: Domain availability (this should work)
    console.log('\n=== TEST 1: Domain Availability (Known Working Endpoint) ===');
    try {
      const params = new URLSearchParams();
      params.append('auth-userid', appConfig.resellerBiz.authUserId);
      params.append('api-key', appConfig.resellerBiz.apiKey);
      params.append('domain-name', 'testdomain123456');
      params.append('tlds', 'com');
      
      const domainUrl = `https://httpapi.com/api/domains/available.json?${params.toString()}`;
      console.log(`Request URL: ${domainUrl}`);
      
      const response = await axios.get(domainUrl, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('✅ Domain availability check successful!');
      console.log('Status:', response.status);
      console.log('Response:', JSON.stringify(response.data, null, 2));
      
    } catch (error: any) {
      console.log('❌ Domain availability check failed');
      console.log('Error:', error.message);
      console.log('Status:', error.response?.status);
      console.log('Response:', error.response?.data);
    }
    
    // Test 2: Products API (this is failing)
    console.log('\n=== TEST 2: Products API (Failing Endpoint) ===');
    try {
      const params = new URLSearchParams();
      params.append('auth-userid', appConfig.resellerBiz.authUserId);
      params.append('api-key', appConfig.resellerBiz.apiKey);
      
      const productsUrl = `https://httpapi.com/api/products/details.json?${params.toString()}`;
      console.log(`Request URL: ${productsUrl}`);
      
      const response = await axios.get(productsUrl, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('✅ Products API successful!');
      console.log('Status:', response.status);
      console.log('Response keys:', Object.keys(response.data || {}));
      
    } catch (error: any) {
      console.log('❌ Products API failed');
      console.log('Error:', error.message);
      console.log('Status:', error.response?.status);
      console.log('Response:', error.response?.data);
      console.log('Headers:', error.response?.headers);
    }
    
    // Test 3: Try different base URLs
    console.log('\n=== TEST 3: Testing Different Base URLs ===');
    const baseUrls = [
      'https://httpapi.com/api',
      'https://test.httpapi.com/api',
      'https://httpapi.com',
      'https://test.httpapi.com'
    ];
    
    for (const baseUrl of baseUrls) {
      try {
        const params = new URLSearchParams();
        params.append('auth-userid', appConfig.resellerBiz.authUserId);
        params.append('api-key', appConfig.resellerBiz.apiKey);
        
        const testUrl = `${baseUrl}/products/details.json?${params.toString()}`;
        console.log(`\nTrying: ${testUrl}`);
        
        const response = await axios.get(testUrl, {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        console.log(`✅ Success with ${baseUrl}`);
        console.log('Status:', response.status);
        
      } catch (error: any) {
        console.log(`❌ Failed with ${baseUrl}: ${error.response?.status || error.message}`);
      }
    }
    
    // Test 4: Try category mapping endpoint
    console.log('\n=== TEST 4: Category Mapping Endpoint ===');
    try {
      const params = new URLSearchParams();
      params.append('auth-userid', appConfig.resellerBiz.authUserId);
      params.append('api-key', appConfig.resellerBiz.apiKey);
      
      const categoryUrl = `https://httpapi.com/api/products/category-keys-mapping.json?${params.toString()}`;
      console.log(`Request URL: ${categoryUrl}`);
      
      const response = await axios.get(categoryUrl, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('✅ Category mapping successful!');
      console.log('Status:', response.status);
      console.log('Response keys:', Object.keys(response.data || {}));
      
    } catch (error: any) {
      console.log('❌ Category mapping failed');
      console.log('Error:', error.message);
      console.log('Status:', error.response?.status);
      console.log('Response:', error.response?.data);
    }
    
    // Test 5: Check if we need different authentication method
    console.log('\n=== TEST 5: Testing POST Method ===');
    try {
      const params = new URLSearchParams();
      params.append('auth-userid', appConfig.resellerBiz.authUserId);
      params.append('api-key', appConfig.resellerBiz.apiKey);
      
      const response = await axios.post(
        'https://httpapi.com/api/products/details.json',
        params.toString(),
        {
          timeout: 30000,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      
      console.log('✅ POST method successful!');
      console.log('Status:', response.status);
      console.log('Response keys:', Object.keys(response.data || {}));
      
    } catch (error: any) {
      console.log('❌ POST method failed');
      console.log('Error:', error.message);
      console.log('Status:', error.response?.status);
      console.log('Response:', error.response?.data);
    }
    
  } catch (error) {
    logger.error('Error in ResellerBiz API debug:', error);
    console.error('Debug failed:', error);
  }
}

// Run the script
if (require.main === module) {
  debugResellerBizAPI()
    .then(() => {
      logger.info('ResellerBiz API debug completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Debug script failed:', error);
      process.exit(1);
    });
}

export { debugResellerBizAPI };
